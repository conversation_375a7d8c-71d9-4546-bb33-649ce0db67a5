package corp.jamaro.jamaroservidor.app.ventas.service;

import corp.jamaro.jamaroservidor.app.repository.ClienteRepository;
import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.producto.repository.ItemRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroservidor.app.ventas.model.BienServicioDevuelto;
import corp.jamaro.jamaroservidor.app.ventas.repository.SaleRepository;
import corp.jamaro.jamaroservidor.app.ventas.repository.BienServicioCargadoRepository;
import corp.jamaro.jamaroservidor.app.ventas.repository.BienServicioDevueltoRepository;
import corp.jamaro.jamaroservidor.app.dinero.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.dinero.model.DevolucionDinero;
import corp.jamaro.jamaroservidor.app.dinero.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.app.dinero.repository.DevolucionDineroRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

/**
 * Servicio para gestionar las operaciones relacionadas con las ventas (Sale).
 *
 * Este servicio implementa el patrón de comunicación RSocket donde:
 * 1. El cliente envía solicitudes de actualización
 * 2. El servidor procesa las solicitudes y devuelve un resultado de éxito/error
 * 3. El cliente recibe las actualizaciones reales a través de su suscripción
 *
 * Refactorizado para trabajar con la nueva estructura de nodos independientes.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SaleService {

    private final SaleRepository saleRepository;
    private final ClienteRepository clienteRepository;
    private final ItemRepository itemRepository;
    private final BienServicioCargadoRepository bienServicioCargadoRepository;
    private final BienServicioDevueltoRepository bienServicioDevueltoRepository;
    private final CobroDineroProgramadoRepository cobroDineroProgramadoRepository;
    private final DevolucionDineroRepository devolucionDineroRepository;

    // Emisor para notificar actualizaciones vía RSocket
    // Utiliza multicast.directBestEffort() para enviar actualizaciones a múltiples suscriptores
    // sin bloquear si algún suscriptor es lento
    private final Sinks.Many<Sale> updateSink = Sinks.many().multicast().directBestEffort();

    /**
     * Permite a los clientes suscribirse a actualizaciones de un Sale específico.
     *
     * El flujo emitirá:
     * 1. El estado actual del Sale como primer elemento (con todas sus relaciones)
     * 2. Todas las actualizaciones futuras que se realicen sobre ese Sale
     *
     * @param saleId ID del Sale al que se quiere suscribir
     * @return Flux que emite el Sale actual y sus actualizaciones futuras
     */
    public Flux<Sale> subscribeToSaleUpdates(UUID saleId) {
        log.info("Suscribiendo a actualizaciones del Sale con ID: {}", saleId);

        // Primero obtenemos el estado actual del Sale con todas sus relaciones
        Mono<Sale> currentState = saleRepository.findSaleWithAllRelationships(saleId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId)));

        // Luego nos suscribimos al flujo de actualizaciones, filtrando solo las del Sale solicitado
        Flux<Sale> updates = updateSink.asFlux()
                .filter(sale -> sale.getId().equals(saleId));

        // Concatenamos el estado actual con las actualizaciones futuras
        return currentState.concatWith(updates);
    }


    /**
     * Emite el estado actual del Sale con todas sus relaciones.
     *
     * @param saleId ID del Sale a emitir
     * @return Mono<Void> que completa cuando la operación termina
     */
    private Mono<Void> emitSale(UUID saleId) {
        return saleRepository.findSaleWithAllRelationships(saleId)
                .doOnNext(freshSale -> {
                    log.debug("Emitiendo actualización para Sale con ID: {}", saleId);
                    updateSink.tryEmitNext(freshSale);
                })
                .then();
    }



    /**
     * Actualiza el Cliente de un Sale.
     *
     * @param saleId ID del Sale a actualizar
     * @param clienteId UUID del Cliente a asignar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> updateCliente(UUID saleId, UUID clienteId) {
        log.debug("Actualizando Cliente en Sale con ID: {}, clienteId: {}", saleId, clienteId);

        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }
                    return clienteRepository.existsById(clienteId);
                })
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Cliente no encontrado con ID: " + clienteId));
                    }
                    return saleRepository.updateCliente(saleId, clienteId);
                })
                .flatMap(id -> emitSale(saleId))
                .thenReturn(true)
                .onErrorResume(e -> {
                    log.error("Error al actualizar Cliente en Sale: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Agrega un Item a un Sale como BienServicioCargado o actualiza la cantidad si ya existe.
     *
     * Lógica de negocio:
     * 1. Si ya existe BienServicioCargado para el Item → incrementa cantidad (ignora precioInicial)
     * 2. Si no existe → crea nuevo BienServicioCargado
     * 3. Si precioInicial es null → usa precioVentaPublico del Item
     * 4. Recalcula totales del Sale automáticamente
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item a agregar
     * @param precioInicial Precio inicial unitario (si es null usa precioVentaPublico del Item)
     * @param cantidad Cantidad a agregar (si es null usa 1.0)
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<Boolean> addItemToSale(UUID saleId, String itemCodCompuesto, Double precioInicial, Double cantidad) {
        log.debug("Agregando Item a Sale con ID: {}, itemCodCompuesto: {}", saleId, itemCodCompuesto);

        // Aplicar valores por defecto
        Double finalCantidad = (cantidad != null) ? cantidad : 1.0;

        // Validaciones básicas
        return validateSaleAndItemExist(saleId, itemCodCompuesto)
                .flatMap(valid -> SecurityUtils.getCurrentUser().map(User::getUsername))
                .flatMap(username -> {
                    // Verificar si ya existe BienServicioCargado para este Item
                    return saleRepository.existsBienServicioCargadoForItem(saleId, itemCodCompuesto)
                        .flatMap(exists -> {
                            if (exists) {
                                // Caso 1: Ya existe → incrementar cantidad usando repositorio específico
                                return incrementExistingBienServicioCargado(saleId, itemCodCompuesto, finalCantidad);
                            } else {
                                // Caso 2: No existe → crear nuevo BienServicioCargado
                                return createNewBienServicioCargado(saleId, itemCodCompuesto, precioInicial, finalCantidad, username);
                            }
                        });
                })
                .flatMap(id -> recalculateAndEmitSale(saleId))
                .thenReturn(true)
                .onErrorResume(e -> {
                    log.error("Error al agregar Item a Sale: {}", e.getMessage());
                    return Mono.just(false);
                });
    }



    /**
     * Actualiza los campos de un BienServicioCargado.
     * Recalcula montoAcordado = precioAcordado * cantidad y actualiza totales del Sale.
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item en la relación
     * @param precioAcordado Nuevo precio acordado unitario
     * @param cantidad Nueva cantidad (opcional, si es null no se actualiza)
     * @param descripcionDelBienServicio Nueva descripción (opcional, si es null no se actualiza)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> updateBienServicioCargado(UUID saleId, String itemCodCompuesto, Double precioAcordado, Double cantidad, String descripcionDelBienServicio) {
        log.debug("Actualizando BienServicioCargado en Sale con ID: {}, itemCodCompuesto: {}, precioAcordado: {}, cantidad: {}, descripcion: {}",
                 saleId, itemCodCompuesto, precioAcordado, cantidad, descripcionDelBienServicio);

        return saleRepository.updateBienServicioCargado(saleId, itemCodCompuesto, precioAcordado, cantidad, descripcionDelBienServicio)
                .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId))
                .flatMap(sale -> emitSale(saleId))
                .thenReturn(true)
                .onErrorResume(e -> {
                    log.error("Error al actualizar BienServicioCargado: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Elimina un BienServicioCargado y actualiza totalMontoInicial, totalMontoAcordado.
     *
     * @param saleId ID del Sale a actualizar
     * @param bienServicioCargadoId ID del BienServicioCargado a eliminar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> deleteBienServicioCargado(UUID saleId, UUID bienServicioCargadoId) {
        log.debug("Eliminando BienServicioCargado de Sale con ID: {}, bienServicioCargadoId: {}", saleId, bienServicioCargadoId);

        return saleRepository.deleteBienServicioCargado(saleId, bienServicioCargadoId)
                .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId))
                .flatMap(sale -> emitSale(saleId))
                .thenReturn(true)
                .onErrorResume(e -> {
                    log.error("Error al eliminar BienServicioCargado: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Elimina todos los nodos BienServicioCargado de un Sale y actualiza totalMontoInicial, totalMontoAcordado.
     *
     * @param saleId ID del Sale a actualizar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> deleteAllBienServicioCargado(UUID saleId) {
        log.debug("Eliminando todos los BienServicioCargado de Sale con ID: {}", saleId);

        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }
                    return saleRepository.deleteAllBienServicioCargado(saleId);
                })
                .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId))
                .flatMap(sale -> emitSale(saleId))
                .thenReturn(true)
                .onErrorResume(e -> {
                    log.error("Error al eliminar todos los BienServicioCargado: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Inicia una venta de contado.
     * Verifica que exista al menos un BienServicioCargado, calcula totales,
     * actualiza el Sale y crea un CobroDineroProgramado.
     *
     * @param saleId ID del Sale a iniciar como venta de contado
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> iniciarVentaContado(UUID saleId) {
        log.debug("Iniciando venta de contado para Sale con ID: {}", saleId);

        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }
                    return saleRepository.hasBienServicioCargado(saleId);
                })
                .flatMap(hasBienServicio -> {
                    if (!hasBienServicio) {
                        return Mono.error(new IllegalArgumentException("El Sale debe tener al menos un BienServicioCargado"));
                    }
                    return SecurityUtils.getCurrentUser().map(User::getUsername);
                })
                .flatMap(username -> saleRepository.iniciarVentaContado(saleId, username))
                .flatMap(sale -> emitSale(saleId))
                .thenReturn(true)
                .onErrorResume(e -> {
                    log.error("Error al iniciar venta de contado: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Inicia un BienServicioDevuelto modificando el BienServicioCargado correspondiente
     * y creando un DevolucionDinero.
     *
     * @param saleId ID del Sale
     * @param bienServicioCargadoId ID del BienServicioCargado a devolver
     * @param bienServicioDevuelto BienServicioDevuelto parcialmente inicializado
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> iniciarBienServicioDevuelto(UUID saleId, UUID bienServicioCargadoId, BienServicioDevuelto bienServicioDevuelto) {
        log.debug("Iniciando BienServicioDevuelto para Sale con ID: {}, bienServicioCargadoId: {}", saleId, bienServicioCargadoId);

        // Validaciones iniciales
        if (bienServicioDevuelto.getCantidad() == null || bienServicioDevuelto.getPrecioAcordadoDevolver() == null) {
            return Mono.error(new IllegalArgumentException("cantidad y precioAcordadoDevolver son requeridos"));
        }

        if (bienServicioDevuelto.getCantidad() <= 0) {
            return Mono.error(new IllegalArgumentException("La cantidad debe ser mayor a cero"));
        }

        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }
                    return saleRepository.getBienServicioCargadoById(bienServicioCargadoId);
                })
                .flatMap(bscData -> {
                    Map<String, Object> bscMap = (Map<String, Object>) bscData.get("bsc");
                    Double bscCantidad = (Double) bscMap.get("cantidad");
                    String bscDescripcion = (String) bscMap.get("descripcionDelBienServicio");

                    // Validar que la cantidad a devolver no sea mayor que la cantidad cargada
                    if (bienServicioDevuelto.getCantidad() > bscCantidad) {
                        return Mono.error(new IllegalArgumentException(
                                "La cantidad a devolver (" + bienServicioDevuelto.getCantidad() +
                                ") no puede ser mayor que la cantidad cargada (" + bscCantidad + ")"));
                    }

                    return SecurityUtils.getCurrentUser().map(User::getUsername)
                            .flatMap(username -> {
                                // Completar el BienServicioDevuelto
                                bienServicioDevuelto.setDevueltoPor(username);
                                if (bienServicioDevuelto.getDescripcionDelBienServicio() == null ||
                                    bienServicioDevuelto.getDescripcionDelBienServicio().isEmpty()) {
                                    bienServicioDevuelto.setDescripcionDelBienServicio(bscDescripcion);
                                }
                                bienServicioDevuelto.setMontoDevuelto(
                                    bienServicioDevuelto.getPrecioAcordadoDevolver() * bienServicioDevuelto.getCantidad());
                                bienServicioDevuelto.setIsDineroDevuelto(false);
                                bienServicioDevuelto.setCreatedAt(Instant.now());

                                // Guardar el BienServicioDevuelto
                                return bienServicioDevueltoRepository.save(bienServicioDevuelto)
                                        .flatMap(savedBsd -> {
                                            // Crear relación con el Sale
                                            return saleRepository.createBienServicioDevueltoRelationship(saleId, savedBsd.getId())
                                                    .flatMap(id -> {
                                                        // Crear relación con el Item del BienServicioCargado
                                                        return saleRepository.createBienServicioDevueltoItemRelationship(savedBsd.getId(), bienServicioCargadoId)
                                                                .flatMap(itemId -> {
                                                                    // Modificar o eliminar el BienServicioCargado
                                                                    if (bienServicioDevuelto.getCantidad().equals(bscCantidad)) {
                                                                        // Eliminar el BienServicioCargado
                                                                        return saleRepository.deleteBienServicioCargadoById(bienServicioCargadoId);
                                                                    } else {
                                                                        // Actualizar la cantidad del BienServicioCargado
                                                                        return saleRepository.updateBienServicioCargadoCantidadById(
                                                                                bienServicioCargadoId, bienServicioDevuelto.getCantidad());
                                                                    }
                                                                })
                                                                .flatMap(bscId -> {
                                                                    // Crear DevolucionDinero
                                                                    DevolucionDinero devolucionDinero = new DevolucionDinero();
                                                                    devolucionDinero.setMontoADevolver(savedBsd.getMontoDevuelto());
                                                                    devolucionDinero.setIniciadoPor(username);
                                                                    devolucionDinero.setCreadoEl(Instant.now());
                                                                    devolucionDinero.setEstaDevuelto(false);

                                                                    return devolucionDineroRepository.save(devolucionDinero)
                                                                            .flatMap(savedDd -> saleRepository.createDevolucionDineroRelationship(saleId, savedDd.getId()));
                                                                });
                                                });
                                        });
                            });
                })
                .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId))
                .flatMap(sale -> emitSale(saleId))
                .thenReturn(true)
                .onErrorResume(e -> {
                    log.error("Error al iniciar BienServicioDevuelto: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

}
